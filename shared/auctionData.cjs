// Shared auction data for both frontend and backend
// This file provides a single source of truth for auction end times

// Auction end times configuration (in seconds from now)
const auctionEndTimes = {
  'fe0': 30,   // 30 seconds
  'fe1': 60,   // 1 minute
  'fe2': 90,   // 1.5 minutes
  'fe3': 120,  // 2 minutes
  'fe4': 150,  // 2.5 minutes
  'fe5': 180,  // 3 minutes
  'fe6': 210,  // 3.5 minutes
  'fe7': 240,  // 4 minutes
  'fe8': 90,   // 1.5 minutes
  'fe9': 60,   // 1 minute
  'fe10': 60,  // 1 minute
  'fe11': 60,  // 1 minute
  'fe12': 60,  // 1 minute
  'fe13': 60,  // 1 minute
  'fe14': 72,  // 1.2 minutes
  'fe15': 72,  // 1.2 minutes
  'pokemon-cards': 72,     // 1.2 minutes
  'first-class-upgrade': 90,  // 1.5 minutes
  'beats-earphones': 108,     // 1.8 minutes
  'bulgari-wonders': 60,      // 1 minute
};

// Default auction configuration
const defaultAuctionConfig = {
  startingBid: 100,
  bidIncrement: 5,
  defaultDuration: 60, // 1 minute default
};

// Helper function to get auction end time
function getAuctionEndTime(auctionId, baseTime = Date.now()) {
  const duration = auctionEndTimes[auctionId] || defaultAuctionConfig.defaultDuration;
  return baseTime + (duration * 1000);
}

// Helper function to get all auction IDs
function getAllAuctionIds() {
  return Object.keys(auctionEndTimes);
}

// Helper function to check if auction ID exists
function isValidAuctionId(auctionId) {
  return auctionEndTimes.hasOwnProperty(auctionId);
}

module.exports = {
  auctionEndTimes,
  defaultAuctionConfig,
  getAuctionEndTime,
  getAllAuctionIds,
  isValidAuctionId
};
